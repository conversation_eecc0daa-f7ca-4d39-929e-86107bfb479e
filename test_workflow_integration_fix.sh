#!/bin/bash

# =============================================================================
# 工作流集成修复验证脚本
# 验证业务Agent服务是否正确调用修复后的工作流执行方法
# =============================================================================

echo "=== 工作流集成修复验证测试开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 创建测试任务
echo -e "${BLUE}1. 创建测试任务（工作流集成修复后）...${NC}"
task_response=$(curl -s -X POST http://localhost:8080/api/compliance-agent/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": 1,
    "taskType": "REGULATION_INTERNALIZATION",
    "title": "工作流集成修复验证测试",
    "description": "验证业务Agent服务是否正确调用工作流执行方法",
    "priority": "HIGH",
    "requestData": "{\"regulationId\":\"REG_WORKFLOW_FIX_001\",\"companyId\":1,\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}"
  }')

task_id=$(echo "$task_response" | jq -r '.taskId')

if [ "$task_id" != "null" ] && [ "$task_id" != "" ]; then
    echo -e "${GREEN}✓ 任务创建成功，任务ID: $task_id${NC}"
else
    echo -e "${RED}✗ 任务创建失败${NC}"
    echo "响应: $task_response"
    exit 1
fi

# 2. 等待任务执行
echo -e "${YELLOW}2. 等待任务执行（25秒）...${NC}"
sleep 25

# 3. 检查AgentTask状态
echo -e "${BLUE}3. 检查AgentTask状态...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    status,
    progress,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time
FROM agent_task 
WHERE id = $task_id;
"

# 4. 检查TaskStep状态（关键验证！）
echo -e "${BLUE}4. 检查TaskStep状态更新（关键验证）...${NC}"
echo "这次应该能看到TaskStep状态正确更新了！"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    step_name,
    step_order,
    status,
    start_time,
    end_time,
    CASE 
        WHEN execution_time IS NOT NULL THEN CONCAT(execution_time, 'ms')
        ELSE 'NULL'
    END as execution_time,
    step_type,
    CASE 
        WHEN input_data IS NOT NULL THEN 'HAS_DATA'
        ELSE 'NULL'
    END as input_data,
    CASE 
        WHEN output_data IS NOT NULL THEN 'HAS_DATA'
        ELSE 'NULL'
    END as output_data
FROM task_step 
WHERE agent_task_id = $task_id 
ORDER BY step_order;
"

# 5. 检查AgentContext状态
echo -e "${BLUE}5. 检查AgentContext状态...${NC}"
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    id,
    agent_task_id,
    context_type,
    created_at
FROM agent_context 
WHERE agent_task_id = $task_id;
"

# 6. 详细的TaskStep分析
echo -e "${BLUE}6. 详细的TaskStep分析...${NC}"

# 统计TaskStep状态分布
echo "TaskStep状态分布："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id), 2) as percentage
FROM task_step 
WHERE agent_task_id = $task_id 
GROUP BY status;
"

# 检查关键字段是否有数据
echo ""
echo "关键字段数据检查："
docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT 
    '总步骤数' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id
UNION ALL
SELECT 
    '有开始时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND start_time IS NOT NULL
UNION ALL
SELECT 
    '有结束时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND end_time IS NOT NULL
UNION ALL
SELECT 
    '有执行时间的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND execution_time IS NOT NULL
UNION ALL
SELECT 
    '有步骤类型的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND step_type IS NOT NULL
UNION ALL
SELECT 
    '状态为COMPLETED的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND status = 'COMPLETED'
UNION ALL
SELECT 
    '状态为RUNNING的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND status = 'RUNNING'
UNION ALL
SELECT 
    '状态为PENDING的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND status = 'PENDING'
UNION ALL
SELECT 
    '有输入数据的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND input_data IS NOT NULL
UNION ALL
SELECT 
    '有输出数据的步骤' as metric,
    COUNT(*) as count
FROM task_step 
WHERE agent_task_id = $task_id AND output_data IS NOT NULL;
"

# 7. 修复效果评估
echo -e "${BLUE}7. 工作流集成修复效果评估...${NC}"

# 获取关键指标
total_steps=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id;
" | tail -n 1)

pending_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'PENDING';
" | tail -n 1)

completed_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'COMPLETED';
" | tail -n 1)

running_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND status = 'RUNNING';
" | tail -n 1)

timestamp_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND start_time IS NOT NULL;
" | tail -n 1)

steptype_count=$(docker exec -it whiskerguardaiservice-mysql-1 mysql -u root whiskerguardaiservice -e "
SELECT COUNT(*) FROM task_step WHERE agent_task_id = $task_id AND step_type IS NOT NULL;
" | tail -n 1)

echo ""
echo -e "${YELLOW}工作流集成修复效果评估结果：${NC}"

echo -e "${BLUE}基础指标：${NC}"
echo "总步骤数: $total_steps"
echo "PENDING状态步骤: $pending_count"
echo "COMPLETED状态步骤: $completed_count"
echo "RUNNING状态步骤: $running_count"

echo ""
echo -e "${BLUE}修复验证：${NC}"

if [ "$pending_count" -eq "$total_steps" ]; then
    echo -e "${RED}❌ 修复失败：所有步骤仍为PENDING状态${NC}"
    echo -e "${RED}   说明工作流执行方法仍未被正确调用${NC}"
elif [ "$completed_count" -gt 0 ]; then
    echo -e "${GREEN}✅ 修复成功：有 $completed_count 个步骤状态为COMPLETED${NC}"
    echo -e "${GREEN}   说明工作流执行方法已被正确调用${NC}"
elif [ "$running_count" -gt 0 ]; then
    echo -e "${YELLOW}⏳ 修复部分成功：有 $running_count 个步骤正在执行${NC}"
    echo -e "${YELLOW}   说明工作流开始执行，但可能还在进行中${NC}"
else
    echo -e "${YELLOW}⚠️  状态不明：需要进一步检查${NC}"
fi

if [ "$timestamp_count" -gt 0 ]; then
    echo -e "${GREEN}✅ 时间戳记录正常：$timestamp_count 个步骤有开始时间${NC}"
else
    echo -e "${RED}❌ 时间戳记录异常：没有步骤记录开始时间${NC}"
fi

if [ "$steptype_count" -gt 0 ]; then
    echo -e "${GREEN}✅ 步骤类型记录正常：$steptype_count 个步骤有类型信息${NC}"
else
    echo -e "${RED}❌ 步骤类型记录异常：没有步骤记录类型信息${NC}"
fi

# 8. 总结
echo ""
echo -e "${GREEN}=== 工作流集成修复验证测试完成 ===${NC}"
echo ""
echo -e "${BLUE}测试任务ID: $task_id${NC}"

# 计算修复成功率
success_checks=0
total_checks=3

[ "$pending_count" -lt "$total_steps" ] && ((success_checks++))
[ "$timestamp_count" -gt 0 ] && ((success_checks++))
[ "$steptype_count" -gt 0 ] && ((success_checks++))

success_rate=$((success_checks * 100 / total_checks))

echo -e "${BLUE}修复成功率: ${success_rate}%${NC}"

if [ "$success_rate" -eq 100 ]; then
    echo -e "${GREEN}🎉 工作流集成修复完全成功！${NC}"
    echo -e "${GREEN}   TaskStep状态正确更新，工作流执行正常${NC}"
elif [ "$success_rate" -ge 67 ]; then
    echo -e "${YELLOW}⚠️  工作流集成修复基本成功，但仍有改进空间${NC}"
else
    echo -e "${RED}❌ 工作流集成修复效果不佳，需要进一步调试${NC}"
fi

echo ""
echo -e "${BLUE}可以通过以下SQL进一步检查：${NC}"
echo "SELECT * FROM task_step WHERE agent_task_id = $task_id ORDER BY step_order;"
echo "SELECT * FROM agent_task WHERE id = $task_id;"
echo "SELECT * FROM agent_context WHERE agent_task_id = $task_id;"
