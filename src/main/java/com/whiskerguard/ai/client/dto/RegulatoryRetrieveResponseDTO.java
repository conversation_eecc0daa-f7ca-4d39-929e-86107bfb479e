package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * RegulatoryRetrieveResponseDTO
 *
 * 法规检索响应DTO，扩展标准检索响应，增加法规特有的上下文信息。
 * 包含检索结果、法规解释、相关案例和企业适用性分析。
 *
 * Regulatory retrieval response DTO that extends standard retrieval response
 * with regulation-specific context information including interpretations, cases, and applicability analysis.
 */
public class RegulatoryRetrieveResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 标准检索结果 / Standard retrieval results */
    private List<RetrieveResponseDTO.Result> results;

    /** 合并后的上下文内容 / Merged context content */
    private String mergedContext;

    /** 上下文格式 / Context format */
    private String contextFormat;

    /** 法规条款级别的检索结果 / Clause-level retrieval results */
    private List<RegulatoryClause> clauses;

    /** 相关案例 / Related cases */
    private List<RegulatoryCase> cases;

    /** 法规解释和指导 / Regulation interpretations and guidance */
    private List<RegulatoryInterpretation> interpretations;

    /** 企业适用性分析 / Enterprise applicability analysis */
    private ApplicabilityAnalysis applicabilityAnalysis;

    /** 检索统计信息 / Retrieval statistics */
    private RetrievalStatistics statistics;

    public RegulatoryRetrieveResponseDTO() {}

    public RegulatoryRetrieveResponseDTO(List<RetrieveResponseDTO.Result> results) {
        this.results = results;
    }

    /**
     * 法规条款内部类
     * Regulatory clause inner class
     */
    public static class RegulatoryClause implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 条款编号 / Clause number */
        private String clauseNumber;

        /** 条款标题 / Clause title */
        private String title;

        /** 条款内容 / Clause content */
        private String content;

        /** 适用范围 / Applicable scope */
        private String applicableScope;

        /** 相似度分数 / Similarity score */
        private Float score;

        /** 条款类型（强制性/推荐性/禁止性） / Clause type */
        private String clauseType;

        // Constructors, getters and setters
        public RegulatoryClause() {}

        public RegulatoryClause(String clauseNumber, String title, String content, Float score) {
            this.clauseNumber = clauseNumber;
            this.title = title;
            this.content = content;
            this.score = score;
        }

        public String getClauseNumber() {
            return clauseNumber;
        }

        public void setClauseNumber(String clauseNumber) {
            this.clauseNumber = clauseNumber;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getApplicableScope() {
            return applicableScope;
        }

        public void setApplicableScope(String applicableScope) {
            this.applicableScope = applicableScope;
        }

        public Float getScore() {
            return score;
        }

        public void setScore(Float score) {
            this.score = score;
        }

        public String getClauseType() {
            return clauseType;
        }

        public void setClauseType(String clauseType) {
            this.clauseType = clauseType;
        }
    }

    /**
     * 法规案例内部类
     * Regulatory case inner class
     */
    public static class RegulatoryCase implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 案例标题 / Case title */
        private String title;

        /** 案例描述 / Case description */
        private String description;

        /** 涉及行业 / Industry involved */
        private String industry;

        /** 企业规模 / Company size */
        private String companySize;

        /** 处理结果 / Handling result */
        private String result;

        /** 相似度分数 / Similarity score */
        private Float score;

        /** 案例来源 / Case source */
        private String source;

        // Constructors, getters and setters
        public RegulatoryCase() {}

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public String getCompanySize() {
            return companySize;
        }

        public void setCompanySize(String companySize) {
            this.companySize = companySize;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public Float getScore() {
            return score;
        }

        public void setScore(Float score) {
            this.score = score;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }
    }

    /**
     * 法规解释内部类
     * Regulatory interpretation inner class
     */
    public static class RegulatoryInterpretation implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 解释标题 / Interpretation title */
        private String title;

        /** 解释内容 / Interpretation content */
        private String content;

        /** 权威来源 / Authoritative source */
        private String authority;

        /** 发布日期 / Publication date */
        private String publicationDate;

        /** 相似度分数 / Similarity score */
        private Float score;

        // Constructors, getters and setters
        public RegulatoryInterpretation() {}

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getAuthority() {
            return authority;
        }

        public void setAuthority(String authority) {
            this.authority = authority;
        }

        public String getPublicationDate() {
            return publicationDate;
        }

        public void setPublicationDate(String publicationDate) {
            this.publicationDate = publicationDate;
        }

        public Float getScore() {
            return score;
        }

        public void setScore(Float score) {
            this.score = score;
        }
    }

    /**
     * 适用性分析内部类
     * Applicability analysis inner class
     */
    public static class ApplicabilityAnalysis implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 总体适用性评分 (0-100) / Overall applicability score */
        private Integer overallScore;

        /** 行业匹配度 / Industry match level */
        private String industryMatch;

        /** 规模适用性 / Size applicability */
        private String sizeApplicability;

        /** 关键合规要点 / Key compliance points */
        private List<String> keyCompliancePoints;

        /** 建议措施 / Recommended actions */
        private List<String> recommendedActions;

        /** 风险等级 / Risk level */
        private String riskLevel;

        // Constructors, getters and setters
        public ApplicabilityAnalysis() {}

        public Integer getOverallScore() {
            return overallScore;
        }

        public void setOverallScore(Integer overallScore) {
            this.overallScore = overallScore;
        }

        public String getIndustryMatch() {
            return industryMatch;
        }

        public void setIndustryMatch(String industryMatch) {
            this.industryMatch = industryMatch;
        }

        public String getSizeApplicability() {
            return sizeApplicability;
        }

        public void setSizeApplicability(String sizeApplicability) {
            this.sizeApplicability = sizeApplicability;
        }

        public List<String> getKeyCompliancePoints() {
            return keyCompliancePoints;
        }

        public void setKeyCompliancePoints(List<String> keyCompliancePoints) {
            this.keyCompliancePoints = keyCompliancePoints;
        }

        public List<String> getRecommendedActions() {
            return recommendedActions;
        }

        public void setRecommendedActions(List<String> recommendedActions) {
            this.recommendedActions = recommendedActions;
        }

        public String getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
        }
    }

    /**
     * 检索统计信息内部类
     * Retrieval statistics inner class
     */
    public static class RetrievalStatistics implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 总检索结果数 / Total results count */
        private Integer totalResults;

        /** 条款数量 / Clauses count */
        private Integer clausesCount;

        /** 案例数量 / Cases count */
        private Integer casesCount;

        /** 解释数量 / Interpretations count */
        private Integer interpretationsCount;

        /** 检索耗时（毫秒） / Retrieval time in milliseconds */
        private Long retrievalTimeMs;

        // Constructors, getters and setters
        public RetrievalStatistics() {}

        public Integer getTotalResults() {
            return totalResults;
        }

        public void setTotalResults(Integer totalResults) {
            this.totalResults = totalResults;
        }

        public Integer getClausesCount() {
            return clausesCount;
        }

        public void setClausesCount(Integer clausesCount) {
            this.clausesCount = clausesCount;
        }

        public Integer getCasesCount() {
            return casesCount;
        }

        public void setCasesCount(Integer casesCount) {
            this.casesCount = casesCount;
        }

        public Integer getInterpretationsCount() {
            return interpretationsCount;
        }

        public void setInterpretationsCount(Integer interpretationsCount) {
            this.interpretationsCount = interpretationsCount;
        }

        public Long getRetrievalTimeMs() {
            return retrievalTimeMs;
        }

        public void setRetrievalTimeMs(Long retrievalTimeMs) {
            this.retrievalTimeMs = retrievalTimeMs;
        }
    }

    // Main class getters and setters
    public List<RetrieveResponseDTO.Result> getResults() {
        return results;
    }

    public void setResults(List<RetrieveResponseDTO.Result> results) {
        this.results = results;
    }

    public String getMergedContext() {
        return mergedContext;
    }

    public void setMergedContext(String mergedContext) {
        this.mergedContext = mergedContext;
    }

    public String getContextFormat() {
        return contextFormat;
    }

    public void setContextFormat(String contextFormat) {
        this.contextFormat = contextFormat;
    }

    public List<RegulatoryClause> getClauses() {
        return clauses;
    }

    public void setClauses(List<RegulatoryClause> clauses) {
        this.clauses = clauses;
    }

    public List<RegulatoryCase> getCases() {
        return cases;
    }

    public void setCases(List<RegulatoryCase> cases) {
        this.cases = cases;
    }

    public List<RegulatoryInterpretation> getInterpretations() {
        return interpretations;
    }

    public void setInterpretations(List<RegulatoryInterpretation> interpretations) {
        this.interpretations = interpretations;
    }

    public ApplicabilityAnalysis getApplicabilityAnalysis() {
        return applicabilityAnalysis;
    }

    public void setApplicabilityAnalysis(ApplicabilityAnalysis applicabilityAnalysis) {
        this.applicabilityAnalysis = applicabilityAnalysis;
    }

    public RetrievalStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(RetrievalStatistics statistics) {
        this.statistics = statistics;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RegulatoryRetrieveResponseDTO that = (RegulatoryRetrieveResponseDTO) o;
        return (
            Objects.equals(results, that.results) &&
            Objects.equals(mergedContext, that.mergedContext) &&
            Objects.equals(contextFormat, that.contextFormat) &&
            Objects.equals(clauses, that.clauses) &&
            Objects.equals(cases, that.cases) &&
            Objects.equals(interpretations, that.interpretations) &&
            Objects.equals(applicabilityAnalysis, that.applicabilityAnalysis) &&
            Objects.equals(statistics, that.statistics)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(results, mergedContext, contextFormat, clauses, cases, interpretations, applicabilityAnalysis, statistics);
    }

    @Override
    public String toString() {
        return (
            "RegulatoryRetrieveResponseDTO{" +
            "resultsCount=" +
            (results != null ? results.size() : 0) +
            ", clausesCount=" +
            (clauses != null ? clauses.size() : 0) +
            ", casesCount=" +
            (cases != null ? cases.size() : 0) +
            ", interpretationsCount=" +
            (interpretations != null ? interpretations.size() : 0) +
            ", contextFormat='" +
            contextFormat +
            '\'' +
            ", hasApplicabilityAnalysis=" +
            (applicabilityAnalysis != null) +
            ", hasStatistics=" +
            (statistics != null) +
            '}'
        );
    }
}
