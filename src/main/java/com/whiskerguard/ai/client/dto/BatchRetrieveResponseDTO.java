package com.whiskerguard.ai.client.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * BatchRetrieveResponseDTO
 *
 * 用于封装批量检索响应结果
 */
public class BatchRetrieveResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量检索结果列表
     */
    private List<RetrieveResponseDTO> results;

    /**
     * 处理时间（毫秒）
     */
    private Long processingTimeMs;

    public BatchRetrieveResponseDTO() {}

    public BatchRetrieveResponseDTO(List<RetrieveResponseDTO> results, Long processingTimeMs) {
        this.results = results;
        this.processingTimeMs = processingTimeMs;
    }

    public List<RetrieveResponseDTO> getResults() {
        return results;
    }

    public void setResults(List<RetrieveResponseDTO> results) {
        this.results = results;
    }

    public Long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(Long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BatchRetrieveResponseDTO)) return false;
        BatchRetrieveResponseDTO that = (BatchRetrieveResponseDTO) o;
        return Objects.equals(results, that.results) && Objects.equals(processingTimeMs, that.processingTimeMs);
    }

    @Override
    public int hashCode() {
        return Objects.hash(results, processingTimeMs);
    }

    @Override
    public String toString() {
        return "BatchRetrieveResponseDTO{" + "results=" + results + ", processingTimeMs=" + processingTimeMs + '}';
    }
}
