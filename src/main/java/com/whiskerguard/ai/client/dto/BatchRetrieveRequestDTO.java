package com.whiskerguard.ai.client.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * BatchRetrieveRequestDTO
 *
 * 用于封装批量检索请求参数
 */
public class BatchRetrieveRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批量查询列表
     */
    @NotEmpty
    @Valid
    private List<RetrieveRequestDTO> queries;

    /**
     * 是否并行处理查询
     */
    private Boolean parallel;

    public BatchRetrieveRequestDTO() {
        this.parallel = true;
    }

    public BatchRetrieveRequestDTO(List<RetrieveRequestDTO> queries) {
        this.queries = queries;
        this.parallel = true;
    }

    public BatchRetrieveRequestDTO(List<RetrieveRequestDTO> queries, Boolean parallel) {
        this.queries = queries;
        this.parallel = parallel;
    }

    public List<RetrieveRequestDTO> getQueries() {
        return queries;
    }

    public void setQueries(List<RetrieveRequestDTO> queries) {
        this.queries = queries;
    }

    public Boolean getParallel() {
        return parallel != null ? parallel : true;
    }

    public void setParallel(Boolean parallel) {
        this.parallel = parallel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BatchRetrieveRequestDTO)) return false;
        BatchRetrieveRequestDTO that = (BatchRetrieveRequestDTO) o;
        return Objects.equals(queries, that.queries) && Objects.equals(parallel, that.parallel);
    }

    @Override
    public int hashCode() {
        return Objects.hash(queries, parallel);
    }

    @Override
    public String toString() {
        return "BatchRetrieveRequestDTO{" + "queries=" + queries + ", parallel=" + parallel + '}';
    }
}
