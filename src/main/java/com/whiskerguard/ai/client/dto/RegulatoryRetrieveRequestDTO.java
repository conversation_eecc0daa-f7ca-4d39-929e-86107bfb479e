package com.whiskerguard.ai.client.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * RegulatoryRetrieveRequestDTO
 *
 * 专门用于法规制度转换场景的检索请求DTO。
 * 在标准检索功能基础上，增加了法规特有的检索参数和企业背景信息。
 *
 * Request DTO specifically designed for regulatory policy conversion scenarios.
 * Extends standard retrieval functionality with regulation-specific parameters and enterprise context.
 */
public class RegulatoryRetrieveRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 法规文本内容，用于语义检索 / Legal regulation text for semantic retrieval */
    @NotBlank
    @Size(min = 1, max = 10000)
    private String regulationText;

    /** 企业行业类型，用于过滤相关案例 / Enterprise industry type for filtering relevant cases */
    @NotBlank
    @Size(min = 1, max = 100)
    private String industryType;

    /** 企业规模（员工数量），用于匹配适用的法规条款 / Enterprise size for matching applicable regulations */
    private Integer companySize;

    /** 企业性质（国企/民企/外企等），影响合规要求 / Enterprise nature affecting compliance requirements */
    private String companyType;

    /** 检索的法规类型（安全生产/环保/财务等） / Type of regulations to retrieve */
    private String regulationType;

    /** 返回的最相关结果数量，不能为空 / Number of most relevant results to return */
    @NotNull
    private Integer topK;

    /** 距离度量方式（cosine/dot），不能为空 / Distance metric method */
    @NotBlank
    private String distanceMetric;

    /** 最大上下文长度（字符数），可选 / Maximum context length in characters */
    private Integer maxContextLength;

    /** 上下文格式（RAW/MARKDOWN/HTML），可选，默认RAW / Context format */
    private String contextFormat;

    /** 是否合并相邻片段，可选，默认false / Whether to merge adjacent chunks */
    private Boolean mergeAdjacentChunks;

    /** 是否包含元数据，可选，默认true（法规检索通常需要元数据） / Whether to include metadata */
    private Boolean includeMetadata;

    /** 向量搜索权重 (0.0-1.0)，可选，默认0.6 / Vector search weight */
    private Float vectorWeight;

    /** 关键词搜索权重 (0.0-1.0)，可选，默认0.4 / Keyword search weight */
    private Float keywordWeight;

    /** 是否启用法规条款级别检索，默认true / Whether to enable clause-level retrieval */
    private Boolean clauseLevelRetrieval;

    /** 是否检索相关案例，默认true / Whether to retrieve related cases */
    private Boolean includeCases;

    /** 是否检索法规解释，默认true / Whether to retrieve regulation interpretations */
    private Boolean includeInterpretations;

    /** 元数据过滤条件，可选 / Metadata filter conditions */
    private List<MetadataFilterDTO> filters;

    public RegulatoryRetrieveRequestDTO() {
        this.topK = 5;
        this.distanceMetric = "cosine";
        this.contextFormat = "RAW";
        this.mergeAdjacentChunks = false;
        this.includeMetadata = true;
        this.vectorWeight = 0.6f;
        this.keywordWeight = 0.4f;
        this.clauseLevelRetrieval = true;
        this.includeCases = true;
        this.includeInterpretations = true;
    }

    public RegulatoryRetrieveRequestDTO(String regulationText, String industryType, Integer topK, String distanceMetric) {
        this();
        this.regulationText = regulationText;
        this.industryType = industryType;
        this.topK = topK;
        this.distanceMetric = distanceMetric;
    }

    // Getters and Setters
    public String getRegulationText() {
        return regulationText;
    }

    public void setRegulationText(String regulationText) {
        this.regulationText = regulationText;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public Integer getCompanySize() {
        return companySize;
    }

    public void setCompanySize(Integer companySize) {
        this.companySize = companySize;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getRegulationType() {
        return regulationType;
    }

    public void setRegulationType(String regulationType) {
        this.regulationType = regulationType;
    }

    public Integer getTopK() {
        return topK;
    }

    public void setTopK(Integer topK) {
        this.topK = topK;
    }

    public String getDistanceMetric() {
        return distanceMetric;
    }

    public void setDistanceMetric(String distanceMetric) {
        this.distanceMetric = distanceMetric;
    }

    public Integer getMaxContextLength() {
        return maxContextLength;
    }

    public void setMaxContextLength(Integer maxContextLength) {
        this.maxContextLength = maxContextLength;
    }

    public String getContextFormat() {
        return contextFormat;
    }

    public void setContextFormat(String contextFormat) {
        this.contextFormat = contextFormat;
    }

    public Boolean getMergeAdjacentChunks() {
        return mergeAdjacentChunks;
    }

    public void setMergeAdjacentChunks(Boolean mergeAdjacentChunks) {
        this.mergeAdjacentChunks = mergeAdjacentChunks;
    }

    public Boolean getIncludeMetadata() {
        return includeMetadata;
    }

    public void setIncludeMetadata(Boolean includeMetadata) {
        this.includeMetadata = includeMetadata;
    }

    public Float getVectorWeight() {
        return vectorWeight;
    }

    public void setVectorWeight(Float vectorWeight) {
        this.vectorWeight = vectorWeight;
    }

    public Float getKeywordWeight() {
        return keywordWeight;
    }

    public void setKeywordWeight(Float keywordWeight) {
        this.keywordWeight = keywordWeight;
    }

    public Boolean getClauseLevelRetrieval() {
        return clauseLevelRetrieval;
    }

    public void setClauseLevelRetrieval(Boolean clauseLevelRetrieval) {
        this.clauseLevelRetrieval = clauseLevelRetrieval;
    }

    public Boolean getIncludeCases() {
        return includeCases;
    }

    public void setIncludeCases(Boolean includeCases) {
        this.includeCases = includeCases;
    }

    public Boolean getIncludeInterpretations() {
        return includeInterpretations;
    }

    public void setIncludeInterpretations(Boolean includeInterpretations) {
        this.includeInterpretations = includeInterpretations;
    }

    public List<MetadataFilterDTO> getFilters() {
        return filters;
    }

    public void setFilters(List<MetadataFilterDTO> filters) {
        this.filters = filters;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RegulatoryRetrieveRequestDTO that = (RegulatoryRetrieveRequestDTO) o;
        return (
            Objects.equals(regulationText, that.regulationText) &&
            Objects.equals(industryType, that.industryType) &&
            Objects.equals(companySize, that.companySize) &&
            Objects.equals(companyType, that.companyType) &&
            Objects.equals(regulationType, that.regulationType) &&
            Objects.equals(topK, that.topK) &&
            Objects.equals(distanceMetric, that.distanceMetric) &&
            Objects.equals(maxContextLength, that.maxContextLength) &&
            Objects.equals(contextFormat, that.contextFormat) &&
            Objects.equals(mergeAdjacentChunks, that.mergeAdjacentChunks) &&
            Objects.equals(includeMetadata, that.includeMetadata) &&
            Objects.equals(vectorWeight, that.vectorWeight) &&
            Objects.equals(keywordWeight, that.keywordWeight) &&
            Objects.equals(clauseLevelRetrieval, that.clauseLevelRetrieval) &&
            Objects.equals(includeCases, that.includeCases) &&
            Objects.equals(includeInterpretations, that.includeInterpretations) &&
            Objects.equals(filters, that.filters)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            regulationText,
            industryType,
            companySize,
            companyType,
            regulationType,
            topK,
            distanceMetric,
            maxContextLength,
            contextFormat,
            mergeAdjacentChunks,
            includeMetadata,
            vectorWeight,
            keywordWeight,
            clauseLevelRetrieval,
            includeCases,
            includeInterpretations,
            filters
        );
    }

    @Override
    public String toString() {
        return (
            "RegulatoryRetrieveRequestDTO{" +
            "regulationText='" +
            (regulationText != null ? regulationText.substring(0, Math.min(100, regulationText.length())) + "..." : null) +
            '\'' +
            ", industryType='" +
            industryType +
            '\'' +
            ", companySize=" +
            companySize +
            ", companyType='" +
            companyType +
            '\'' +
            ", regulationType='" +
            regulationType +
            '\'' +
            ", topK=" +
            topK +
            ", distanceMetric='" +
            distanceMetric +
            '\'' +
            ", clauseLevelRetrieval=" +
            clauseLevelRetrieval +
            ", includeCases=" +
            includeCases +
            ", includeInterpretations=" +
            includeInterpretations +
            '}'
        );
    }
}
