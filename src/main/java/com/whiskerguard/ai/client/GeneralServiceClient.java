/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：GeneralServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：通用服务客户端接口（天眼查功能）
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.FileOperationRequestDTO;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 通用服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与通用服务（whiskerguard-general-service）进行通信。
 * 主要提供天眼查企业信息查询功能，支持合同审查中的企业背景调查。
 *
 * 主要功能：
 * 1. 查询企业基本工商信息
 * 2. 获取企业风险评估信息
 * 3. 查询企业诉讼和法律纠纷记录
 * 4. 获取企业信用状况和评级
 * 5. 腾讯云COS文件读取功能
 * 6. 支持多租户数据隔离
 */
@FeignClient(name = "whiskerguardgeneralservice", configuration = GeneralServiceClient.GeneralServiceClientConfiguration.class)
public interface GeneralServiceClient {
    /**
     * 查询企业基本信息
     * <p>
     * 通过天眼查API获取企业的基本工商信息，
     * 包括注册资本、成立时间、经营状态、所属行业等。
     *
     * @param keyword 搜索关键词（企业名称、ID、注册号或统一社会信用代码）
     * @return 企业基本信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/basic-info")
    String getCompanyBasicInfo(@RequestParam("keyword") String keyword);

    /**
     * 查询企业风险信息
     * <p>
     * 获取企业的风险评估信息，包括经营异常、
     * 行政处罚、严重违法失信等风险指标。
     *
     * @param keyword 搜索关键词
     * @return 企业风险信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/risk")
    String getCompanyRiskInfo(@RequestParam("keyword") String keyword);

    /**
     * 查询企业联系方式信息
     * <p>
     * 获取企业的联系电话、邮箱、地址等联系方式信息。
     *
     * @param keyword 搜索关键词
     * @return 企业联系方式信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/contact")
    String getCompanyContactInfo(@RequestParam("keyword") String keyword);

    /**
     * 查询企业变更记录
     * <p>
     * 获取企业的工商变更记录，包括法人变更、注册资本变更等。
     *
     * @param keyword 搜索关键词
     * @return 企业变更记录（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/change-records")
    String getCompanyChangeRecords(@RequestParam("keyword") String keyword);

    /**
     * 查询企业类型信息
     * <p>
     * 获取企业的类型分类信息。
     *
     * @param keyword 搜索关键词
     * @return 企业类型信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/type")
    String getCompanyTypeInfo(@RequestParam("keyword") String keyword);

    /**
     * 查询企业工商信息
     * <p>
     * 获取企业的经营范围、许可证信息、
     * 资质证书等经营相关信息。
     *
     * @param keyword 搜索关键词
     * @return 企业工商信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/business")
    String getCompanyBusinessInfo(@RequestParam("keyword") String keyword);

    /**
     * 查询企业失信人记录
     * <p>
     * 获取企业相关的失信人记录信息。
     *
     * @param keyword 搜索关键词
     * @return 企业失信人记录（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/dishonest-persons")
    String getCompanyDishonestPersons(@RequestParam("keyword") String keyword);

    /**
     * 查询企业立案信息
     * <p>
     * 获取企业相关的立案信息。
     *
     * @param keyword 搜索关键词
     * @return 企业立案信息（JSON格式）
     */
    @GetMapping("/api/tianyancha/company/case-filings")
    String getCompanyCaseFilings(@RequestParam("keyword") String keyword);

    /**
     * 验证企业三要素
     * <p>
     * 验证企业名称、统一社会信用代码和法人姓名的一致性。
     *
     * @param request 验证请求，包含企业名称、信用代码和法人姓名
     * @return 验证结果（JSON格式）
     */
    @PostMapping("/api/tianyancha/company/verify")
    String verifyCompanyThreeElements(@RequestBody CompanyVerificationRequest request);

    /**
     * 强制刷新企业数据
     * <p>
     * 强制从天眼查API刷新企业数据，获取最新信息。
     *
     * @param keyword 搜索关键词
     * @return 刷新后的企业信息（JSON格式）
     */
    @PostMapping("/api/tianyancha/company/refresh")
    String refreshCompanyData(@RequestParam("keyword") String keyword);

    // ========== COS文件服务相关接口 ==========

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    @PostMapping("/api/file/cos/content")
    String readFileContent(@RequestBody FileOperationRequestDTO request);

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件是否存在的布尔值
     */
    @PostMapping("/api/file/cos/exists")
    Boolean checkFileExists(@RequestBody FileOperationRequestDTO request);

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     *
     * @param request 文件操作请求DTO，包含文件名和租户ID
     * @return 文件信息的JSON字符串
     */
    @PostMapping("/api/file/cos/info")
    String getFileInfo(@RequestBody FileOperationRequestDTO request);

    /**
     * 企业三要素验证请求DTO
     * <p>
     * 用于验证企业名称、统一社会信用代码和法人姓名的一致性。
     */
    class CompanyVerificationRequest {

        private String companyName; // 企业名称
        private String creditCode; // 统一社会信用代码
        private String legalPersonName; // 法人姓名

        public CompanyVerificationRequest() {}

        public CompanyVerificationRequest(String companyName, String creditCode, String legalPersonName) {
            this.companyName = companyName;
            this.creditCode = creditCode;
            this.legalPersonName = legalPersonName;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }

        public String getCreditCode() {
            return creditCode;
        }

        public void setCreditCode(String creditCode) {
            this.creditCode = creditCode;
        }

        public String getLegalPersonName() {
            return legalPersonName;
        }

        public void setLegalPersonName(String legalPersonName) {
            this.legalPersonName = legalPersonName;
        }

        @Override
        public String toString() {
            return (
                "CompanyVerificationRequest{" +
                "companyName='" +
                companyName +
                '\'' +
                ", creditCode='" +
                creditCode +
                '\'' +
                ", legalPersonName='" +
                legalPersonName +
                '\'' +
                '}'
            );
        }
    }

    /**
     * 通用服务客户端专用配置类
     * <p>
     * 配置认证拦截器和超时设置，
     * 优化天眼查API调用的性能和可靠性。
     */
    @Configuration
    class GeneralServiceClientConfiguration {

        /**
         * 配置用户认证拦截器
         * 自动传递JWT token和用户信息
         */
        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        /**
         * 配置Feign请求选项
         * 天眼查API调用可能需要较长时间，特别是批量查询
         * 文件读取也可能需要较长时间，特别是大文件
         */
        @Bean
        public Request.Options feignRequestOptions() {
            return new Request.Options(
                30000, // 连接超时 5 秒
                60000 // 读取超时 30 秒 - 天眼查API和文件读取可能需要较长时间
            );
        }
    }
}
