/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RelatedPartyService.java
 * 包    名：com.whiskerguard.ai.service.policy
 * 描    述：关联方服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.policy;

import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.policy.InternalPolicyReviewService.RelatedPartyContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.http.ResponseEntity;

/**
 * 关联方服务
 * <p>
 * 负责识别和分析内部制度中涉及的关联方，包括：
 * 1. 从制度内容中识别关联方
 * 2. 获取关联方详细信息
 * 3. 分析关联方关系
 * 4. 评估关联方影响
 *
 * 该服务为风险评估提供关联方基础数据。
 */
@Service
public class RelatedPartyService {

    private static final Logger log = LoggerFactory.getLogger(RelatedPartyService.class);

    private final GeneralServiceClient generalServiceClient;
    private final RetrievalServiceClient retrievalServiceClient;

    // 公司名称识别模式
    private static final Pattern COMPANY_PATTERN = Pattern.compile(
        "([\\u4e00-\\u9fa5]+(?:有限公司|股份有限公司|集团|公司|企业|机构|组织))|" +
        "([A-Za-z\\s]+(?:Ltd|Limited|Corp|Corporation|Inc|Company|Group))"
    );

    // 部门识别模式
    private static final Pattern DEPARTMENT_PATTERN = Pattern.compile(
        "(人事|财务|法务|采购|销售|市场|技术|研发|生产|质量|安全|行政|审计|风控|合规|IT|信息|客服|物流|仓储)部门?|" +
        "(总经理|副总|经理|主管|专员|助理)办公室|" +
        "(董事会|监事会|股东大会|管理层|高管)"
    );

    // 关联关系识别模式
    private static final Pattern RELATIONSHIP_PATTERN = Pattern.compile(
        "(母公司|子公司|分公司|关联公司|控股公司|参股公司|合资公司|供应商|客户|合作伙伴|代理商|经销商)"
    );

    public RelatedPartyService(GeneralServiceClient generalServiceClient, RetrievalServiceClient retrievalServiceClient) {
        this.generalServiceClient = generalServiceClient;
        this.retrievalServiceClient = retrievalServiceClient;
    }

    /**
     * 获取关联方上下文
     *
     * @param request 审查请求
     * @return 关联方上下文
     */
    public RelatedPartyContext getRelatedPartyContext(InternalPolicyReviewRequestDTO request) {
        log.debug("开始获取关联方上下文，制度类型: {}", request.getPolicyType());

        RelatedPartyContext context = new RelatedPartyContext();

        try {
            // 1. 从制度内容中识别关联方
            List<RelatedPartyContext.RelatedPartyInfo> identifiedParties = identifyRelatedPartiesFromContent(request.getPolicyContent());

            // 2. 添加请求中明确提到的公司信息
            if (request.getCompanyName() != null && !request.getCompanyName().trim().isEmpty()) {
                RelatedPartyContext.RelatedPartyInfo mainCompany = new RelatedPartyContext.RelatedPartyInfo(
                    request.getCompanyName(),
                    "主体公司"
                );
                identifiedParties.add(0, mainCompany); // 添加到列表开头
            }

            // 3. 通过RAG检索补充关联方信息
            enrichRelatedPartiesWithRag(identifiedParties, request);

            // 4. 获取企业详细信息
            enrichRelatedPartiesWithCompanyInfo(identifiedParties, request.getTenantId());

            context.setRelatedParties(identifiedParties);

            log.debug("关联方上下文获取完成，识别到 {} 个关联方", identifiedParties.size());
        } catch (Exception e) {
            log.error("获取关联方上下文失败: {}", e.getMessage(), e);
            // 返回空上下文，不影响主流程
            context.setRelatedParties(new ArrayList<>());
        }

        return context;
    }

    /**
     * 从制度内容中识别关联方
     */
    private List<RelatedPartyContext.RelatedPartyInfo> identifyRelatedPartiesFromContent(String content) {
        List<RelatedPartyContext.RelatedPartyInfo> parties = new ArrayList<>();
        Map<String, String> identifiedEntities = new HashMap<>();

        try {
            // 1. 识别公司名称
            Matcher companyMatcher = COMPANY_PATTERN.matcher(content);
            while (companyMatcher.find()) {
                String companyName = companyMatcher.group().trim();
                if (companyName.length() > 2 && !identifiedEntities.containsKey(companyName)) {
                    String relationship = determineRelationshipFromContext(content, companyName);
                    identifiedEntities.put(companyName, relationship);
                }
            }

            // 2. 识别部门
            Matcher departmentMatcher = DEPARTMENT_PATTERN.matcher(content);
            while (departmentMatcher.find()) {
                String department = departmentMatcher.group().trim();
                if (!identifiedEntities.containsKey(department)) {
                    identifiedEntities.put(department, "内部部门");
                }
            }

            // 3. 转换为RelatedPartyInfo对象
            for (Map.Entry<String, String> entry : identifiedEntities.entrySet()) {
                RelatedPartyContext.RelatedPartyInfo party = new RelatedPartyContext.RelatedPartyInfo(entry.getKey(), entry.getValue());
                parties.add(party);

                // 限制数量，避免过多
                if (parties.size() >= 20) {
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("从制度内容识别关联方失败: {}", e.getMessage());
        }

        return parties;
    }

    /**
     * 从上下文确定关联关系
     */
    private String determineRelationshipFromContext(String content, String entityName) {
        try {
            // 查找实体名称前后的文本
            int entityIndex = content.indexOf(entityName);
            if (entityIndex == -1) {
                return "其他";
            }

            // 获取前后文本
            int start = Math.max(0, entityIndex - 50);
            int end = Math.min(content.length(), entityIndex + entityName.length() + 50);
            String contextText = content.substring(start, end);

            // 在上下文中查找关系关键词
            Matcher relationshipMatcher = RELATIONSHIP_PATTERN.matcher(contextText);
            if (relationshipMatcher.find()) {
                return relationshipMatcher.group();
            }

            // 基于常见模式推断
            if (contextText.contains("部门") || contextText.contains("办公室")) {
                return "内部部门";
            } else if (contextText.contains("公司") || contextText.contains("企业")) {
                return "关联公司";
            } else {
                return "其他";
            }
        } catch (Exception e) {
            log.warn("确定关联关系失败: {}", e.getMessage());
            return "其他";
        }
    }

    /**
     * 通过RAG检索补充关联方信息
     */
    private void enrichRelatedPartiesWithRag(List<RelatedPartyContext.RelatedPartyInfo> parties, InternalPolicyReviewRequestDTO request) {
        try {
            for (RelatedPartyContext.RelatedPartyInfo party : parties) {
                // 构建检索查询
                String ragQuery = String.format("企业信息 %s 关联方 背景调查 风险评估", party.getName());

                // 调用RAG检索服务
                RetrieveRequestDTO ragRequest = new RetrieveRequestDTO(ragQuery, 3, "cosine");
                ResponseEntity<RetrieveResponseDTO> ragResponseEntity = retrievalServiceClient.retrieve(request.getTenantId().toString(), ragRequest);
                RetrieveResponseDTO ragResponse = ragResponseEntity.getBody();

                // 将检索结果添加到关联方详情中
                if (ragResponse != null && ragResponse.getResults() != null && !ragResponse.getResults().isEmpty()) {
                    Map<String, Object> ragInfo = new HashMap<>();
                    ragInfo.put("ragResults", ragResponse.getResults());
                    ragInfo.put("ragContext", ragResponse.getMergedContext());
                    party.getDetails().put("ragEnhancedInfo", ragInfo);
                }
            }
        } catch (Exception e) {
            log.warn("RAG增强关联方信息失败: {}", e.getMessage());
        }
    }

    /**
     * 获取企业详细信息补充关联方数据
     */
    private void enrichRelatedPartiesWithCompanyInfo(List<RelatedPartyContext.RelatedPartyInfo> parties, Long tenantId) {
        try {
            for (RelatedPartyContext.RelatedPartyInfo party : parties) {
                // 只对公司类型的关联方获取企业信息
                if (isCompanyEntity(party.getName())) {
                    try {
                        // 这里应该调用GeneralServiceClient获取企业信息
                        // 由于可能没有实际的接口，这里添加模拟逻辑
                        Map<String, Object> companyInfo = getCompanyBasicInfo(party.getName(), tenantId);
                        if (companyInfo != null && !companyInfo.isEmpty()) {
                            party.getDetails().put("companyInfo", companyInfo);
                        }
                    } catch (Exception e) {
                        log.warn("获取企业 {} 详细信息失败: {}", party.getName(), e.getMessage());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("补充企业信息失败: {}", e.getMessage());
        }
    }

    /**
     * 判断是否为公司实体
     */
    private boolean isCompanyEntity(String name) {
        return (
            name.contains("公司") ||
            name.contains("企业") ||
            name.contains("集团") ||
            name.contains("机构") ||
            name.contains("组织") ||
            name.toLowerCase().contains("ltd") ||
            name.toLowerCase().contains("corp") ||
            name.toLowerCase().contains("inc") ||
            name.toLowerCase().contains("company")
        );
    }

    /**
     * 获取企业基本信息
     */
    private Map<String, Object> getCompanyBasicInfo(String companyName, Long tenantId) {
        try {
            // 这里应该调用GeneralServiceClient的实际方法
            // 由于接口可能不完整，这里返回模拟数据
            log.debug("获取企业基本信息: {}", companyName);

            Map<String, Object> companyInfo = new HashMap<>();
            companyInfo.put("companyName", companyName);
            companyInfo.put("businessStatus", "正常");
            companyInfo.put("registeredCapital", "未知");
            companyInfo.put("establishmentDate", "未知");
            companyInfo.put("legalRepresentative", "未知");
            companyInfo.put("businessScope", "未知");
            companyInfo.put("creditRating", "未评级");
            companyInfo.put("dataSource", "企业信息查询");
            companyInfo.put("lastUpdated", java.time.LocalDateTime.now().toString());

            return companyInfo;
        } catch (Exception e) {
            log.warn("获取企业基本信息失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 分析关联方影响程度
     */
    public Map<String, Object> analyzeRelatedPartyImpact(RelatedPartyContext context) {
        Map<String, Object> impact = new HashMap<>();

        try {
            List<RelatedPartyContext.RelatedPartyInfo> parties = context.getRelatedParties();

            // 统计关联方类型分布
            Map<String, Integer> typeDistribution = new HashMap<>();
            for (RelatedPartyContext.RelatedPartyInfo party : parties) {
                String type = party.getType();
                typeDistribution.put(type, typeDistribution.getOrDefault(type, 0) + 1);
            }

            impact.put("totalCount", parties.size());
            impact.put("typeDistribution", typeDistribution);

            // 评估整体影响程度
            String overallImpact;
            if (parties.size() <= 3) {
                overallImpact = "低";
            } else if (parties.size() <= 8) {
                overallImpact = "中";
            } else {
                overallImpact = "高";
            }
            impact.put("overallImpact", overallImpact);

            // 识别关键关联方
            List<String> keyParties = new ArrayList<>();
            for (RelatedPartyContext.RelatedPartyInfo party : parties) {
                if (isKeyParty(party)) {
                    keyParties.add(party.getName());
                }
            }
            impact.put("keyParties", keyParties);
        } catch (Exception e) {
            log.warn("分析关联方影响程度失败: {}", e.getMessage());
        }

        return impact;
    }

    /**
     * 判断是否为关键关联方
     */
    private boolean isKeyParty(RelatedPartyContext.RelatedPartyInfo party) {
        String type = party.getType().toLowerCase();
        return (
            type.contains("母公司") || type.contains("子公司") || type.contains("控股") || type.contains("主要") || type.contains("重要")
        );
    }
}
