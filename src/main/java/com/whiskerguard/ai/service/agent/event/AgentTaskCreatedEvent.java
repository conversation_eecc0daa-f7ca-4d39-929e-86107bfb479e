/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskCreatedEvent.java
 * 包    名：com.whiskerguard.ai.service.agent.event
 * 描    述：Agent任务创建事件
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.event;

import org.springframework.context.ApplicationEvent;

/**
 * Agent任务创建事件
 * <p>
 * 当Agent任务在数据库中成功创建后发布此事件。
 * 通过事件机制确保异步任务执行在事务提交后进行，避免竞争条件。
 *
 * <AUTHOR>
 * @since 1.0
 */
public class AgentTaskCreatedEvent extends ApplicationEvent {

    private final Long taskId;

    /**
     * 构造函数
     *
     * @param taskId 任务ID
     */
    public AgentTaskCreatedEvent(Long taskId) {
        super(taskId);
        this.taskId = taskId;
    }

    /**
     * 获取任务ID
     *
     * @return 任务ID
     */
    public Long getTaskId() {
        return taskId;
    }

    @Override
    public String toString() {
        return "AgentTaskCreatedEvent{" +
               "taskId=" + taskId +
               '}';
    }
}
