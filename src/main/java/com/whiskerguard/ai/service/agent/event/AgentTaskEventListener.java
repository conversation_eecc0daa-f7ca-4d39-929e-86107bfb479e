/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：AgentTaskEventListener.java
 * 包    名：com.whiskerguard.ai.service.agent.event
 * 描    述：Agent任务事件监听器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/7/1
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.event;

import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.service.agent.ComplianceAgentService;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * Agent任务事件监听器
 * <p>
 * 监听Agent任务相关事件，确保在事务提交后执行相应的操作。
 * 主要用于解决并发竞争条件问题，保证异步任务执行时数据已经持久化。
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class AgentTaskEventListener {

    private static final Logger log = LoggerFactory.getLogger(AgentTaskEventListener.class);

    private final AgentTaskRepository agentTaskRepository;
    private final ComplianceAgentService complianceAgentService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public AgentTaskEventListener(
        AgentTaskRepository agentTaskRepository,
        ComplianceAgentService complianceAgentService
    ) {
        this.agentTaskRepository = agentTaskRepository;
        this.complianceAgentService = complianceAgentService;
    }

    /**
     * 处理任务创建事件
     * <p>
     * 在事务提交后触发，确保任务已经成功保存到数据库后再开始异步执行。
     * 这样可以避免异步线程在主事务提交前就尝试查询任务而导致的竞争条件。
     *
     * @param event 任务创建事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    @Async
    public void handleTaskCreated(AgentTaskCreatedEvent event) {
        Long taskId = event.getTaskId();
        log.info("处理任务创建事件，任务ID: {}", taskId);

        try {
            // 从数据库获取任务实体，此时事务已经提交，数据确实存在
            Optional<AgentTask> taskOpt = agentTaskRepository.findById(taskId);
            if (taskOpt.isEmpty()) {
                log.error("任务创建事件处理失败，任务不存在，ID: {}", taskId);
                return;
            }

            AgentTask task = taskOpt.get();
            log.info("开始异步执行任务，ID: {}, 类型: {}", taskId, task.getTaskType());

            // 异步执行任务
            complianceAgentService.executeTaskAsync(task);
        } catch (Exception e) {
            log.error("处理任务创建事件失败，任务ID: {}", taskId, e);
        }
    }
}
