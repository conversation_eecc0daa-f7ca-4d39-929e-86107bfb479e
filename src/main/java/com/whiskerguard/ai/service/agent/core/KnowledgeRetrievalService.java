/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：KnowledgeRetrievalService.java
 * 包    名：com.whiskerguard.ai.service.agent.core
 * 描    述：知识检索服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.core;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.client.dto.RegulatoryRetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RegulatoryRetrieveResponseDTO;
import com.whiskerguard.ai.domain.KnowledgeCache;
import com.whiskerguard.ai.repository.KnowledgeCacheRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;

/**
 * 知识检索服务
 * <p>
 * 负责从RAG服务检索知识，并提供缓存机制。
 * 统一管理知识检索的接口和缓存策略。
 *
 * 主要功能：
 * 1. 知识检索和缓存
 * 2. 缓存管理和过期处理
 * 3. 检索结果优化
 * 4. 多租户数据隔离
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class KnowledgeRetrievalService {

    private static final Logger log = LoggerFactory.getLogger(KnowledgeRetrievalService.class);

    private final RetrievalServiceClient retrievalServiceClient;
    private final KnowledgeCacheRepository knowledgeCacheRepository;

    /**
     * 缓存过期时间（小时）
     */
    @Value("${application.knowledge-cache.expire-hours:24}")
    private int cacheExpireHours = 24;

    /**
     * 最小缓存文本长度
     */
    @Value("${application.knowledge-cache.min-text-length:100}")
    private int minCacheTextLength = 100;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public KnowledgeRetrievalService(RetrievalServiceClient retrievalServiceClient, KnowledgeCacheRepository knowledgeCacheRepository) {
        this.retrievalServiceClient = retrievalServiceClient;
        this.knowledgeCacheRepository = knowledgeCacheRepository;
    }

    /**
     * 检索知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param queryParams 查询参数
     * @return 知识内容
     */
    public String retrieveKnowledge(Long tenantId, String knowledgeType, String queryKey, Map<String, Object> queryParams) {
        log.debug("检索知识内容，租户: {}, 类型: {}, 关键词: {}", tenantId, knowledgeType, queryKey);

        try {
            // 1. 尝试从缓存获取
            Optional<String> cachedContent = getCachedKnowledge(tenantId, knowledgeType, queryKey);
            if (cachedContent.isPresent()) {
                log.debug("从缓存获取知识内容成功");
                return cachedContent.orElseThrow();
            }

            // 2. 从RAG服务检索
            String content = retrieveFromRagService(tenantId, knowledgeType, queryParams);

            // 3. 缓存检索结果
            if (StringUtils.hasText(content) && content.length() >= minCacheTextLength) {
                cacheKnowledge(tenantId, knowledgeType, queryKey, content);
            }

            return content;
        } catch (Exception e) {
            log.error("检索知识内容失败", e);
            throw new RuntimeException("知识检索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从缓存获取知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @return 可选的缓存内容
     */
    private Optional<String> getCachedKnowledge(Long tenantId, String knowledgeType, String queryKey) {
        try {
            // 检查是否存在有效缓存
            Optional<KnowledgeCache> cacheOpt = knowledgeCacheRepository.findByTenantIdAndKnowledgeTypeAndQueryKey(
                tenantId,
                knowledgeType,
                queryKey
            );

            if (cacheOpt.isPresent()) {
                KnowledgeCache cache = cacheOpt.orElseThrow();
                // 检查缓存是否过期
                if (
                    cache.getCreatedAt().plusSeconds((long) cacheExpireHours * 3600).isAfter(Instant.now()) &&
                    StringUtils.hasText(cache.getContent())
                ) {
                    log.debug("找到有效缓存，缓存ID: {}", cache.getId());
                    return Optional.of(cache.getContent());
                } else {
                    log.debug("缓存已过期，缓存ID: {}", cache.getId());
                    // 删除过期缓存
                    knowledgeCacheRepository.delete(cache);
                }
            }
            return Optional.empty();
        } catch (Exception e) {
            log.warn("获取缓存知识失败", e);
            return Optional.empty();
        }
    }

    /**
     * 缓存知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param content 内容
     */
    private void cacheKnowledge(Long tenantId, String knowledgeType, String queryKey, String content) {
        try {
            // 检查是否已存在缓存
            Optional<KnowledgeCache> existingCache = knowledgeCacheRepository.findByTenantIdAndKnowledgeTypeAndQueryKey(
                tenantId,
                knowledgeType,
                queryKey
            );

            KnowledgeCache cache;
            if (existingCache.isPresent()) {
                // 更新现有缓存
                cache = existingCache.orElseThrow();
                cache.setContent(content);
                cache.setUpdatedAt(Instant.now());
            } else {
                // 创建新缓存
                cache = new KnowledgeCache();
                cache.setTenantId(tenantId);
                cache.setKnowledgeType(knowledgeType);
                cache.setQueryKey(queryKey);
                cache.setContent(content);
                cache.setCreatedAt(Instant.now());
                cache.setUpdatedAt(Instant.now());
                cache.setCreatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
            }

            cache.setExpireTime(Instant.now().plusSeconds((long) cacheExpireHours * 3600)); // 设置过期时间
            knowledgeCacheRepository.save(cache);
            log.debug("缓存知识内容成功，缓存ID: {}", cache.getId());
        } catch (Exception e) {
            log.warn("缓存知识内容失败", e);
        }
    }

    /**
     * 从RAG服务检索知识内容
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryParams 查询参数
     * @return 检索结果
     */
    @Retryable(retryFor = { RestClientException.class }, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2))
    private String retrieveFromRagService(Long tenantId, String knowledgeType, Map<String, Object> queryParams) {
        log.debug("从RAG服务检索知识，租户: {}, 类型: {}", tenantId, knowledgeType);

        try {
            String tenantIdStr = String.valueOf(tenantId);
            String result = "";

            // 根据知识类型调用不同的检索接口
            switch (knowledgeType) {
                case "REGULATION", "COMPLIANCE", "LEGAL", "CONTRACT", "POLICY" -> {
                    // 使用法规专用检索
                    RegulatoryRetrieveRequestDTO regulatoryRequest = new RegulatoryRetrieveRequestDTO();
                    regulatoryRequest.setRegulationText(String.valueOf(queryParams.getOrDefault("query", "")));
                    regulatoryRequest.setIndustryType(String.valueOf(queryParams.getOrDefault("industryType", "通用")));
                    regulatoryRequest.setTopK((Integer) queryParams.getOrDefault("topK", 5));

                    ResponseEntity<RegulatoryRetrieveResponseDTO> response = retrievalServiceClient.regulatoryRetrieve(tenantIdStr, regulatoryRequest);
                    if (response.getBody() != null && response.getBody().getResults() != null) {
                        // 将检索结果合并为字符串
                        StringBuilder sb = new StringBuilder();
                        response.getBody().getResults().forEach(item -> {
                            if (item.getContent() != null) {
                                sb.append(item.getContent()).append("\n");
                            }
                        });
                        result = sb.toString();
                    }
                }
                default -> {
                    // 使用通用向量检索
                    RetrieveRequestDTO retrieveRequest = new RetrieveRequestDTO();
                    retrieveRequest.setQuery(String.valueOf(queryParams.getOrDefault("query", "")));
                    retrieveRequest.setTopK((Integer) queryParams.getOrDefault("topK", 5));

                    ResponseEntity<RetrieveResponseDTO> response = retrievalServiceClient.retrieve(tenantIdStr, retrieveRequest);
                    if (response.getBody() != null && response.getBody().getResults() != null) {
                        // 将检索结果合并为字符串
                        StringBuilder sb = new StringBuilder();
                        response.getBody().getResults().forEach(item -> {
                            if (item.getContent() != null) {
                                sb.append(item.getContent()).append("\n");
                            }
                        });
                        result = sb.toString();
                    }
                }
            }

            log.debug("RAG服务检索完成，结果长度: {}", result != null ? result.length() : 0);
            return Objects.requireNonNullElse(result, "");
        } catch (Exception e) {
            log.error("RAG服务检索失败", e);
            throw new RuntimeException("RAG服务检索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接检索知识内容（不使用缓存）
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryParams 查询参数
     * @return 知识内容
     */
    public String retrieveKnowledgeDirectly(Long tenantId, String knowledgeType, Map<String, Object> queryParams) {
        log.debug("直接检索知识内容，租户: {}, 类型: {}", tenantId, knowledgeType);
        return retrieveFromRagService(tenantId, knowledgeType, queryParams);
    }

    /**
     * 检索知识内容并返回详细信息
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param queryParams 查询参数
     * @return 包含内容和元数据的Map
     */
    public Map<String, Object> retrieveKnowledgeWithMetadata(Long tenantId, String knowledgeType, String queryKey, Map<String, Object> queryParams) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查缓存
            Optional<String> cachedContent = getCachedKnowledge(tenantId, knowledgeType, queryKey);
            boolean fromCache = cachedContent.isPresent();

            String content;
            if (fromCache) {
                content = cachedContent.orElseThrow();
                result.put("fromCache", true);
            } else {
                content = retrieveFromRagService(tenantId, knowledgeType, queryParams);
                result.put("fromCache", false);

                // 缓存结果
                if (StringUtils.hasText(content) && content.length() >= minCacheTextLength) {
                    cacheKnowledge(tenantId, knowledgeType, queryKey, content);
                }
            }

            result.put("content", content);
            result.put("knowledgeType", knowledgeType);
            result.put("queryKey", queryKey);
            result.put("retrievalTime", Instant.now());

            return result;
        } catch (Exception e) {
            log.error("检索知识内容失败", e);
            result.put("error", e.getMessage());
            result.put("content", "");
            return result;
        }
    }

    /**
     * 清理指定类型的过期缓存
     *
     * @param knowledgeType 知识类型
     * @return 清理的缓存数量
     */
    @Transactional
    public int cleanExpiredCache(String knowledgeType) {
        Instant expirationTime = Instant.now().minusSeconds(cacheExpireHours * 3600);
        int count = knowledgeCacheRepository.deleteByKnowledgeTypeAndCreatedAtBefore(knowledgeType, expirationTime);
        log.info("清理过期缓存完成，类型: {}, 数量: {}", knowledgeType, count);
        return count;
    }

    /**
     * 清理所有过期缓存
     *
     * @return 清理的缓存数量
     */
    @Transactional
    public int cleanAllExpiredCache() {
        Instant expirationTime = Instant.now().minusSeconds(cacheExpireHours * 3600);
        int count = knowledgeCacheRepository.deleteByCreatedAtBefore(expirationTime);
        log.info("清理所有过期缓存完成，数量: {}", count);
        return count;
    }

    /**
     * 更新知识缓存
     *
     * @param tenantId 租户ID
     * @param knowledgeType 知识类型
     * @param queryKey 查询关键词
     * @param content 新的知识内容
     * @return 更新是否成功
     */
    @Transactional
    public boolean updateKnowledgeCache(Long tenantId, String knowledgeType, String queryKey, String content) {
        try {
            Optional<KnowledgeCache> cacheOpt = knowledgeCacheRepository.findByTenantIdAndKnowledgeTypeAndQueryKey(
                tenantId,
                knowledgeType,
                queryKey
            );

            if (cacheOpt.isPresent()) {
                KnowledgeCache cache = cacheOpt.orElseThrow();
                cache.setContent(content);
                cache.setUpdatedAt(Instant.now());
                cache.setUpdatedBy(SecurityUtils.getCurrentUserLogin().orElse("system"));
                cache.setVersion(cache.getVersion() + 1);
                knowledgeCacheRepository.save(cache);
                log.debug("更新知识缓存成功，缓存ID: {}", cache.getId());
                return true;
            } else {
                // 如果不存在，则创建新缓存
                cacheKnowledge(tenantId, knowledgeType, queryKey, content);
                return true;
            }
        } catch (Exception e) {
            log.error("更新知识缓存失败", e);
            return false;
        }
    }
}
