/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：InternalPolicyReviewServiceTest.java
 * 包    名：com.whiskerguard.ai.service.policy
 * 描    述：内部制度审查服务测试类
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/16
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.service.policy;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.InternalPolicyDTO;
import com.whiskerguard.ai.client.dto.LegalRegulationDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.policy.InternalPolicyReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.policy.PolicyAnalysisService;
import com.whiskerguard.ai.service.policy.PolicyRiskAssessmentService;
import com.whiskerguard.ai.service.policy.RelatedPartyService;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

/**
 * 内部制度审查服务测试类
 * <p>
 * 测试内部制度智能审查功能的各个方面，包括：
 * 1. 基本的审查流程
 * 2. 异常处理
 * 3. 参数验证
 * 4. 服务集成
 */
@ExtendWith(MockitoExtension.class)
class InternalPolicyReviewServiceTest {

    @Mock
    private AiInvocationService aiInvocationService;

    @Mock
    private RegulatoryServiceClient regulatoryServiceClient;

    @Mock
    private GeneralServiceClient generalServiceClient;

    @Mock
    private RetrievalServiceClient retrievalServiceClient;

    @Mock
    private PolicyAnalysisService policyAnalysisService;

    @Mock
    private PolicyRiskAssessmentService policyRiskAssessmentService;

    @Mock
    private RelatedPartyService relatedPartyService;

    @Mock
    private AiToolMetricsService metricsService;

    @Mock
    private PromptBuilderService promptBuilderService;

    private ObjectMapper objectMapper;
    private InternalPolicyReviewService internalPolicyReviewService;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        internalPolicyReviewService = new InternalPolicyReviewService(
            aiInvocationService,
            regulatoryServiceClient,
            generalServiceClient,
            retrievalServiceClient,
            policyAnalysisService,
            policyRiskAssessmentService,
            relatedPartyService,
            metricsService,
            objectMapper,
            promptBuilderService
        );
    }

    @Test
    void testReviewInternalPolicy_Success() {
        // 准备测试数据
        InternalPolicyReviewRequestDTO request = createTestRequest();

        // Mock 依赖服务的返回值
        mockDependencyServices();

        // Mock AI服务返回
        AiRequestDTO aiResult = createMockAiResult();
        when(aiInvocationService.invoke(any())).thenReturn(aiResult);

        // 执行测试
        InternalPolicyReviewResponseDTO response = internalPolicyReviewService.reviewInternalPolicy(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getReviewId());
        assertNotNull(response.getOverallRiskLevel());
        assertNotNull(response.getRiskScore());
        assertNotNull(response.getReviewTime());
        assertNotNull(response.getReviewDuration());
        assertEquals("COMPLETED", response.getReviewStatus());

        // 验证服务调用
        verify(policyAnalysisService).analyzePolicyContent(request);
        verify(relatedPartyService).getRelatedPartyContext(request);
        verify(aiInvocationService).invoke(any());
    }

    @Test
    void testReviewInternalPolicy_NullRequest() {
        // 测试空请求
        assertThrows(IllegalArgumentException.class, () -> {
            internalPolicyReviewService.reviewInternalPolicy(null);
        });
    }

    @Test
    void testReviewInternalPolicy_EmptyPolicyContent() {
        // 测试空制度内容
        InternalPolicyReviewRequestDTO request = createTestRequest();
        request.setPolicyContent("");

        assertThrows(IllegalArgumentException.class, () -> {
            internalPolicyReviewService.reviewInternalPolicy(request);
        });
    }

    @Test
    void testReviewInternalPolicy_NullTenantId() {
        // 测试空租户ID
        InternalPolicyReviewRequestDTO request = createTestRequest();
        request.setTenantId(null);

        assertThrows(IllegalArgumentException.class, () -> {
            internalPolicyReviewService.reviewInternalPolicy(request);
        });
    }

    @Test
    void testReviewInternalPolicy_NullEmployeeId() {
        // 测试空员工ID
        InternalPolicyReviewRequestDTO request = createTestRequest();
        request.setEmployeeId(null);

        assertThrows(IllegalArgumentException.class, () -> {
            internalPolicyReviewService.reviewInternalPolicy(request);
        });
    }

    @Test
    void testReviewInternalPolicy_AiServiceFailure() {
        // 准备测试数据
        InternalPolicyReviewRequestDTO request = createTestRequest();

        // Mock 依赖服务 - 使用lenient模式
        lenient()
            .when(policyAnalysisService.analyzePolicyContent(any()))
            .thenReturn(new InternalPolicyReviewService.PolicyAnalysisContext());
        lenient().when(relatedPartyService.getRelatedPartyContext(any())).thenReturn(new InternalPolicyReviewService.RelatedPartyContext());
        lenient().when(policyRiskAssessmentService.assessRelatedPartyRisks(any())).thenReturn(new ArrayList<>());
        lenient().when(regulatoryServiceClient.getRegulationsByContractType(anyString(), anyLong())).thenReturn(new ArrayList<>());
        lenient().when(regulatoryServiceClient.getRegulationsByIndustry(anyString(), anyLong())).thenReturn(new ArrayList<>());
        lenient().when(regulatoryServiceClient.getInternalPolicies(anyLong(), anyString())).thenReturn(new ArrayList<>());
        RetrieveResponseDTO ragResponse = new RetrieveResponseDTO();
        ragResponse.setResults(new ArrayList<>());
        ragResponse.setMergedContext("测试上下文");
        lenient().when(retrievalServiceClient.retrieve(anyString(), any())).thenReturn(ResponseEntity.ok(ragResponse));

        // Mock AI服务抛出异常
        when(aiInvocationService.invoke(any())).thenThrow(new RuntimeException("AI服务不可用"));

        // 执行测试并验证异常
        assertThrows(InternalPolicyReviewService.PolicyReviewException.class, () -> {
            internalPolicyReviewService.reviewInternalPolicy(request);
        });
    }

    /**
     * 创建测试请求
     */
    private InternalPolicyReviewRequestDTO createTestRequest() {
        InternalPolicyReviewRequestDTO request = new InternalPolicyReviewRequestDTO();
        request.setPolicyContent(
            "第一条 为了规范公司财务管理，确保财务信息的真实性和准确性，制定本制度。\n" +
            "第二条 本制度适用于公司所有部门和员工。\n" +
            "第三条 财务部门负责公司的财务管理工作，包括会计核算、财务分析、预算管理等。"
        );
        request.setPolicyType("财务制度");
        request.setPolicyTitle("财务管理制度");
        request.setDepartment("财务部");
        request.setTenantId(1L);
        request.setEmployeeId(100L);
        request.setCompanyName("测试公司");
        request.setIndustry("制造业");
        return request;
    }

    /**
     * Mock 依赖服务
     */
    private void mockDependencyServices() {
        // Mock 制度分析服务
        InternalPolicyReviewService.PolicyAnalysisContext analysisContext = new InternalPolicyReviewService.PolicyAnalysisContext();
        analysisContext.setKeyClauses(List.of("第一条 规范财务管理", "第二条 适用范围", "第三条 财务部门职责"));
        when(policyAnalysisService.analyzePolicyContent(any())).thenReturn(analysisContext);

        // Mock 关联方服务
        InternalPolicyReviewService.RelatedPartyContext relatedPartyContext = new InternalPolicyReviewService.RelatedPartyContext();
        when(relatedPartyService.getRelatedPartyContext(any())).thenReturn(relatedPartyContext);

        // Mock 风险评估服务
        when(policyRiskAssessmentService.assessRelatedPartyRisks(any())).thenReturn(new ArrayList<>());

        // Mock 法规服务
        when(regulatoryServiceClient.getRegulationsByContractType(anyString(), anyLong())).thenReturn(createMockLegalRegulations());
        when(regulatoryServiceClient.getRegulationsByIndustry(anyString(), anyLong())).thenReturn(createMockLegalRegulations());
        when(regulatoryServiceClient.getInternalPolicies(anyLong(), anyString())).thenReturn(createMockInternalPolicies());

        // Mock RAG检索服务
        RetrieveResponseDTO ragResponse = new RetrieveResponseDTO();
        ragResponse.setResults(new ArrayList<>());
        ragResponse.setMergedContext("相关制度审查最佳实践");
        when(retrievalServiceClient.retrieve(anyString(), any())).thenReturn(ResponseEntity.ok(ragResponse));
    }

    /**
     * 创建Mock法律法规
     */
    private List<LegalRegulationDTO> createMockLegalRegulations() {
        List<LegalRegulationDTO> regulations = new ArrayList<>();
        LegalRegulationDTO regulation = new LegalRegulationDTO();
        regulation.setId(1L);
        regulation.setName("企业财务通则");
        regulation.setFullText("企业应当建立健全财务管理制度");
        regulations.add(regulation);
        return regulations;
    }

    /**
     * 创建Mock内部制度
     */
    private List<InternalPolicyDTO> createMockInternalPolicies() {
        List<InternalPolicyDTO> policies = new ArrayList<>();
        InternalPolicyDTO policy = new InternalPolicyDTO();
        policy.setId(1L);
        policy.setTenantId(1L);
        policy.setPolicyName("财务管理制度");
        policy.setCategory("财务制度");
        policies.add(policy);
        return policies;
    }

    /**
     * 创建Mock AI结果
     */
    private AiRequestDTO createMockAiResult() {
        AiRequestDTO aiResult = new AiRequestDTO();
        aiResult.setId(1L);
        aiResult.setToolType("deepseek");
        aiResult.setResponse(
            "{\n" +
            "  \"overallRiskLevel\": \"MEDIUM\",\n" +
            "  \"riskScore\": 35,\n" +
            "  \"riskSummary\": \"制度整体风险可控，存在少量需要完善的条款\",\n" +
            "  \"riskPoints\": [],\n" +
            "  \"clauseIssues\": [],\n" +
            "  \"overallRecommendations\": [\"建议完善财务审批流程\"],\n" +
            "  \"priorityActions\": [\"更新财务管理条款\"],\n" +
            "  \"confidence\": 85\n" +
            "}"
        );
        return aiResult;
    }
}
